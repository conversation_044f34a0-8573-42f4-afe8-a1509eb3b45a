{"name": "sse-sample-project", "version": "1.0.0", "description": "A sample project demonstrating Server-Sent Events (SSE) functionality", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "install-all": "npm install && cd server && npm install && cd ../client && npm install", "build": "cd client && npm run build", "start": "cd server && npm start"}, "keywords": ["sse", "server-sent-events", "real-time", "websockets", "streaming"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}